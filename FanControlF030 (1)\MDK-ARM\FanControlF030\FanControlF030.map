Component: Arm Compiler for Embedded 6.19 Tool: armlink [5e73cb00]

==============================================================================

Section Cross References

    startup_stm32f030x8.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f030x8.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f030x8.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f030x8.o(RESET) refers to startup_stm32f030x8.o(STACK) for __initial_sp
    startup_stm32f030x8.o(RESET) refers to startup_stm32f030x8.o(.text) for Reset_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler) for DMA1_Channel2_3_IRQHandler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler) for DMA1_Channel4_5_IRQHandler
    startup_stm32f030x8.o(RESET) refers to stm32f0xx_it.o(.text.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_stm32f030x8.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f030x8.o(.text) refers to system_stm32f0xx.o(.text.SystemInit) for SystemInit
    startup_stm32f030x8.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f030x8.o(.text) refers to startup_stm32f030x8.o(HEAP) for Heap_Mem
    startup_stm32f030x8.o(.text) refers to startup_stm32f030x8.o(STACK) for Stack_Mem
    main.o(.text.TransmitMsgString) refers to strlen.o(.text) for strlen
    main.o(.text.TransmitMsgString) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(.text.TransmitMsgString) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for HAL_UART_AbortTransmit
    main.o(.text.TransmitMsgString) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(.text.TransmitMsgString) refers to usart.o(.bss.huart2) for huart2
    main.o(.ARM.exidx.text.TransmitMsgString) refers to main.o(.text.TransmitMsgString) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f0xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC_Init) for MX_ADC_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM17_Init) for MX_TIM17_Init
    main.o(.text.main) refers to iwdg.o(.text.MX_IWDG_Init) for MX_IWDG_Init
    main.o(.text.main) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(.text.main) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.main) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT) for HAL_ADC_Start_IT
    main.o(.text.main) refers to stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Refresh) for HAL_IWDG_Refresh
    main.o(.text.main) refers to stm32f0xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to ntc10k.o(.text.NTC_Read_Resistor) for NTC_Read_Resistor
    main.o(.text.main) refers to ntc10k.o(.text.NTC_Calculate) for NTC_Calculate
    main.o(.text.main) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(.text.main) refers to fcmp.o(i._fgr) for __aeabi_fcmpgt
    main.o(.text.main) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    main.o(.text.main) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    main.o(.text.main) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    main.o(.text.main) refers to tim.o(.bss.htim1) for htim1
    main.o(.text.main) refers to iwdg.o(.bss.hiwdg) for hiwdg
    main.o(.text.main) refers to stm32f0xx_it.o(.bss..L_MergedGlobals) for ADC_raw
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    main.o(.text.main) refers to main.o(.data.PinIn_State) for PinIn_State
    main.o(.text.main) refers to adc.o(.bss.hadc) for hadc
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.SystemClock_Config) refers to stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC_Init) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC_Init) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.text.MX_ADC_Init) refers to adc.o(.bss.hadc) for hadc
    adc.o(.ARM.exidx.text.MX_ADC_Init) refers to adc.o(.text.MX_ADC_Init) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(.text.HAL_ADC_MspInit) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    iwdg.o(.text.MX_IWDG_Init) refers to stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init) for HAL_IWDG_Init
    iwdg.o(.text.MX_IWDG_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    iwdg.o(.text.MX_IWDG_Init) refers to iwdg.o(.bss.hiwdg) for hiwdg
    iwdg.o(.ARM.exidx.text.MX_IWDG_Init) refers to iwdg.o(.text.MX_IWDG_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM3_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(.text.MX_TIM3_Init) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM3_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM17_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM17_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM17_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(.text.MX_TIM17_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(.text.MX_TIM17_Init) refers to tim.o(.bss.htim17) for htim17
    tim.o(.ARM.exidx.text.MX_TIM17_Init) refers to tim.o(.text.MX_TIM17_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    tim.o(.text.HAL_TIM_Base_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.HAL_TIM_Base_MspInit) refers to tim.o(.bss.hdma_tim17_ch1_up) for hdma_tim17_ch1_up
    tim.o(.text.HAL_TIM_Base_MspInit) refers to tim.o(.bss.hdma_tim3_ch1_trig) for hdma_tim3_ch1_trig
    tim.o(.text.HAL_TIM_Base_MspInit) refers to tim.o(.bss.hdma_tim3_ch3) for hdma_tim3_ch3
    tim.o(.text.HAL_TIM_Base_MspInit) refers to tim.o(.bss.hdma_tim3_ch4_up) for hdma_tim3_ch4_up
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f0xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f0xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f0xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f0xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f0xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f0xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f0xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.SysTick_Handler) refers to stm32f0xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f0xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f0xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler) refers to tim.o(.bss.hdma_tim17_ch1_up) for hdma_tim17_ch1_up
    stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler) refers to stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler) refers to tim.o(.bss.hdma_tim3_ch3) for hdma_tim3_ch3
    stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler) refers to tim.o(.bss.hdma_tim3_ch4_up) for hdma_tim3_ch4_up
    stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel2_3_IRQHandler) refers to stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler) refers to tim.o(.bss.hdma_tim3_ch1_trig) for hdma_tim3_ch1_trig
    stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel4_5_IRQHandler) refers to stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_GetValue) for HAL_ADC_GetValue
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to adc.o(.bss.hadc) for hadc
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to stm32f0xx_it.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_it.o(.text.ADC1_IRQHandler) refers to main.o(.data.PinIn_State) for PinIn_State
    stm32f0xx_it.o(.ARM.exidx.text.ADC1_IRQHandler) refers to stm32f0xx_it.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f0xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Init) refers to adc.o(.text.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Init) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f0xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start) refers to stm32f0xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Start) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.ADC_Enable) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_adc.o(.text.ADC_Enable) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.text.ADC_Enable) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_Enable) refers to stm32f0xx_hal_adc.o(.text.ADC_Enable) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForConversion) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForEvent) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForEvent) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT) refers to stm32f0xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_IT) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAConvCplt) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.ADC_DMAError) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError) refers to stm32f0xx_hal_adc.o(.text.ADC_DMAError) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_GetValue) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError) refers to stm32f0xx_hal_adc.o(.text.HAL_ADC_GetError) for [Anonymous Symbol]
    stm32f0xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start) refers to stm32f0xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f0xx_hal_rcc.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f0xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to stm32f0xx_hal_rcc.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f0xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_Init) refers to stm32f0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f0xx_hal.o(.text.HAL_Init) refers to stm32f0xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f0xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_InitTick) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal.o(.text.HAL_InitTick) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f0xx_hal.o(.text.HAL_InitTick) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f0xx_hal.o(.text.HAL_InitTick) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_InitTick) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f0xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f0xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_DeInit) refers to stm32f0xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f0xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f0xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_IncTick) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_IncTick) refers to stm32f0xx_hal.o(.bss.uwTick) for uwTick
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f0xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_GetTick) refers to stm32f0xx_hal.o(.bss.uwTick) for uwTick
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f0xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f0xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f0xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f0xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f0xx_hal.o(.text.HAL_Delay) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal.o(.text.HAL_Delay) refers to stm32f0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f0xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f0xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f0xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f0xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f0xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f0xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f0xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f0xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f0xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Master_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Slave_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Master_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAMasterTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAMasterReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Slave_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMASlaveTransmitCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMASlaveReceiveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Mem_ISR_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Mem_ISR_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32f0xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITMasterSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITMasterCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITSlaveCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITListenCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITListenCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITListenCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITSlaveSeqCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_ITAddrCplt) refers to stm32f0xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITAddrCplt) refers to stm32f0xx_hal_i2c.o(.text.I2C_ITAddrCplt) for [Anonymous Symbol]
    stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_IsErrorOccurred) refers to stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred) for [Anonymous Symbol]
    stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigAnalogFilter) refers to stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter) for [Anonymous Symbol]
    stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigDigitalFilter) refers to stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter) for [Anonymous Symbol]
    stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_EnableFastModePlus) refers to stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_EnableFastModePlus) for [Anonymous Symbol]
    stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_DisableFastModePlus) refers to stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_DisableFastModePlus) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.text.HAL_DMA_Init) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f0xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f0xx_hal_flash_ex.o(.text.FLASH_PageErase) for FLASH_PageErase
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f0xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f0xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.text.FLASH_PageErase) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase) refers to stm32f0xx_hal_flash_ex.o(.text.FLASH_PageErase) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData) refers to stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f0xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_iwdg.o(.ARM.exidx.text.HAL_IWDG_Init) refers to stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init) for [Anonymous Symbol]
    stm32f0xx_hal_iwdg.o(.ARM.exidx.text.HAL_IWDG_Refresh) refers to stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Refresh) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f0xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f0xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f0xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f0xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f0xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f0xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f0xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32f0xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f0xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f0xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_AdvFeatureConfig) refers to stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f0xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f0xx_hal_uart.o(.text.UART_SetConfig) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f0xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) refers to stm32f0xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_CheckIdleState) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f0xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout) refers to stm32f0xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f0xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32f0xx_hal_uart.o(.text.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32f0xx_hal_uart.o(.text.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT) refers to stm32f0xx_hal_uart.o(.text.UART_TxISR_16BIT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT) refers to stm32f0xx_hal_uart.o(.text.UART_TxISR_8BIT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_IT) refers to stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32f0xx_hal_uart.o(.text.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32f0xx_hal_uart.o(.text.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f0xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMAError) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f0xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f0xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f0xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ReceiverTimeout_Config) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_ReceiverTimeout_Config) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_EnableReceiverTimeout) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_EnableReceiverTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DisableReceiverTimeout) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_DisableReceiverTimeout) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnableMuteMode) refers to stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_DisableMuteMode) refers to stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_RxISR_16BIT) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f0xx_hal_uart.o(.text.UART_RxISR_16BIT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT) refers to stm32f0xx_hal_uart.o(.text.UART_RxISR_16BIT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_RxISR_8BIT) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f0xx_hal_uart.o(.text.UART_RxISR_8BIT) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT) refers to stm32f0xx_hal_uart.o(.text.UART_RxISR_8BIT) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f0xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_RS485Ex_Init) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32f0xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    system_stm32f0xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f0xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f0xx.o(.text.SystemCoreClockUpdate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system_stm32f0xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f0xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f0xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    ntc10k.o(.text.NTC_Read_Resistor) refers to ldexp.o(i.ldexp) for ldexp
    ntc10k.o(.text.NTC_Read_Resistor) refers to daddsub.o(.text) for __aeabi_dadd
    ntc10k.o(.text.NTC_Read_Resistor) refers to dflti.o(.text) for __aeabi_i2d
    ntc10k.o(.text.NTC_Read_Resistor) refers to d2f.o(.text) for __aeabi_d2f
    ntc10k.o(.text.NTC_Read_Resistor) refers to fflti.o(.text) for __aeabi_i2f
    ntc10k.o(.text.NTC_Read_Resistor) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ntc10k.o(.text.NTC_Read_Resistor) refers to fdiv.o(.text) for __aeabi_fdiv
    ntc10k.o(.ARM.exidx.text.NTC_Read_Resistor) refers to ntc10k.o(.text.NTC_Read_Resistor) for [Anonymous Symbol]
    ntc10k.o(.text.NTC_Calculate) refers to fdiv.o(.text) for __aeabi_fdiv
    ntc10k.o(.text.NTC_Calculate) refers to f2d.o(.text) for __aeabi_f2d
    ntc10k.o(.text.NTC_Calculate) refers to log.o(i.log) for log
    ntc10k.o(.text.NTC_Calculate) refers to ddiv.o(.text) for __aeabi_ddiv
    ntc10k.o(.text.NTC_Calculate) refers to daddsub.o(.text) for __aeabi_dadd
    ntc10k.o(.text.NTC_Calculate) refers to d2f.o(.text) for __aeabi_d2f
    ntc10k.o(.ARM.exidx.text.NTC_Calculate) refers to ntc10k.o(.text.NTC_Calculate) for [Anonymous Symbol]
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.ldexp) refers to dscalbn.o(.text) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    log.o(i.__softfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.log) for log
    log.o(i.log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.log) refers to _rserrno.o(.text) for __set_errno
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.log) refers to dscalbn.o(.text) for __ARM_scalbn
    log.o(i.log) refers to daddsub.o(.text) for __aeabi_dsub
    log.o(i.log) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log.o(i.log) refers to dflti.o(.text) for __aeabi_i2d
    log.o(i.log) refers to dmul.o(.text) for __aeabi_dmul
    log.o(i.log) refers to ddiv.o(.text) for __aeabi_ddiv
    log.o(i.log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.log) refers to log.o(.constdata) for .constdata
    log.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalbn.o(.text) for __ARM_scalbn
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f030x8.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.text.TransmitMsgString), (60 bytes).
    Removing main.o(.ARM.exidx.text.TransmitMsgString), (8 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.SystemClock_Config), (124 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.bss.BufferSend), (200 bytes).
    Removing main.o(.bss.Data), (32 bytes).
    Removing main.o(.bss.PulseWidth), (16 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing adc.o(.text.HAL_ADC_MspDeInit), (52 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing iwdg.o(.text), (0 bytes).
    Removing iwdg.o(.ARM.exidx.text.MX_IWDG_Init), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (76 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM17_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_PWM_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (128 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.text.MX_USART1_UART_Init), (56 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.text.MX_USART2_UART_Init), (56 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspInit), (156 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (80 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.bss.huart1), (136 bytes).
    Removing usart.o(.bss.huart2), (136 bytes).
    Removing stm32f0xx_it.o(.text), (0 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel2_3_IRQHandler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.DMA1_Channel4_5_IRQHandler), (8 bytes).
    Removing stm32f0xx_it.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f0xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text), (0 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_DeInit), (280 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_Start), (88 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_Enable), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop), (204 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForConversion), (286 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_PollForEvent), (240 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_IT), (212 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_DMA), (164 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.ADC_DMAConvCplt), (114 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.ADC_DMAError), (26 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_Stop_DMA), (396 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig), (140 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_GetState), (4 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState), (8 bytes).
    Removing stm32f0xx_hal_adc.o(.text.HAL_ADC_GetError), (4 bytes).
    Removing stm32f0xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError), (8 bytes).
    Removing stm32f0xx_hal_adc_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start), (174 bytes).
    Removing stm32f0xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_DeInit), (184 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq), (76 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (76 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (20 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq), (32 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (132 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (52 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (48 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (172 bytes).
    Removing stm32f0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f0xx_hal.o(.text), (0 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_DeInit), (32 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_SetTickFreq), (36 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_SuspendTick), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_ResumeTick), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetREVID), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetDEVID), (20 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Init), (180 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (448 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (250 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_WaitOnTXISFlagUntilTimeout), (166 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXISFlagUntilTimeout), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_WaitOnSTOPFlagUntilTimeout), (134 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnSTOPFlagUntilTimeout), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (428 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (200 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (546 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (370 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (180 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_IT), (412 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Master_ISR_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_IT), (508 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Slave_ISR_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (84 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (352 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Master_ISR_DMA), (484 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Master_ISR_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterTransmitCplt), (180 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAMasterTransmitCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMAError), (24 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (324 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMAMasterReceiveCplt), (188 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAMasterReceiveCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (272 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Slave_ISR_DMA), (288 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Slave_ISR_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveTransmitCplt), (152 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMASlaveTransmitCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (216 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMASlaveReceiveCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (560 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (560 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_IT), (500 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Mem_ISR_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (172 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (296 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_Mem_ISR_DMA), (504 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_Mem_ISR_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (300 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (500 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (296 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (552 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (196 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (400 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (228 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_DMAAbort), (62 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (388 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (224 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (384 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (44 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (200 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (18 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (130 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITError), (380 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITMasterSeqCplt), (96 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITMasterSeqCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITMasterCplt), (264 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITMasterCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveCplt), (892 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITSlaveCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITListenCplt), (96 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITListenCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITSlaveSeqCplt), (132 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITSlaveSeqCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_ITAddrCplt), (168 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_ITAddrCplt), (8 bytes).
    Removing stm32f0xx_hal_i2c.o(.text.I2C_IsErrorOccurred), (384 bytes).
    Removing stm32f0xx_hal_i2c.o(.ARM.exidx.text.I2C_IsErrorOccurred), (8 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter), (66 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigAnalogFilter), (8 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter), (66 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigDigitalFilter), (8 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_EnableFastModePlus), (8 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.text.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32f0xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_DisableFastModePlus), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (40 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_Start), (104 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_Start_IT), (130 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort), (62 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (66 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (278 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (70 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (102 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f0xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (32 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (32 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (48 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (24 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (28 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (24 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f0xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f0xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program), (340 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (172 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (348 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (152 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f0xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (208 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.FLASH_PageErase), (36 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (112 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase), (180 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (448 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData), (28 bytes).
    Removing stm32f0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (160 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (128 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (96 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f0xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f0xx_hal_iwdg.o(.text), (0 bytes).
    Removing stm32f0xx_hal_iwdg.o(.ARM.exidx.text.HAL_IWDG_Init), (8 bytes).
    Removing stm32f0xx_hal_iwdg.o(.ARM.exidx.text.HAL_IWDG_Refresh), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_Base_SetConfig), (184 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (44 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (84 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (160 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (30 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMAError), (90 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Init), (244 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start), (192 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (148 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (220 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (180 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (468 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (256 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (148 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (220 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (180 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (468 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (256 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start), (180 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (108 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (212 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (132 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (396 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMACaptureCplt), (112 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (212 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (252 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (72 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (116 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (132 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (148 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (336 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (72 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (148 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (136 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (184 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (536 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (216 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (442 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (544 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_OC2_SetConfig), (128 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_TI1_SetConfig), (80 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (540 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (22 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (360 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMATriggerCplt), (30 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (22 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (360 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (34 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (244 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (78 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (192 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (78 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (56 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (32 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f0xx_hal_tim.o(.rodata.cst16), (32 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (196 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (72 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (116 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (120 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (72 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (188 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (164 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (132 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (208 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (184 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (380 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (64 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (192 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (164 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (132 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (208 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (184 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (380 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (192 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (120 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (110 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (136 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (86 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (86 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (120 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (32 bytes).
    Removing stm32f0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Init), (104 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_AdvFeatureConfig), (210 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_AdvFeatureConfig), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_SetConfig), (304 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_CheckIdleState), (196 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_CheckIdleState), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_DeInit), (60 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit), (408 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout), (444 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Receive), (236 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (116 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_TxISR_16BIT), (96 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_TxISR_8BIT), (88 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_IT), (102 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_Start_Receive_IT), (188 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (176 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMATransmitCplt), (72 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMAError), (178 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (264 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_Start_Receive_DMA), (168 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_DMAResume), (136 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_DMAStop), (258 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Abort), (248 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (110 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive), (178 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_Abort_IT), (296 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMATxAbortCallback), (58 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMARxAbortCallback), (66 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (136 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (212 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_IRQHandler), (984 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMAAbortOnError), (22 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_ReceiverTimeout_Config), (16 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ReceiverTimeout_Config), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_EnableReceiverTimeout), (50 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_EnableReceiverTimeout), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_DisableReceiverTimeout), (50 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DisableReceiverTimeout), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode), (58 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnableMuteMode), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode), (58 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_DisableMuteMode), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (82 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (82 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_GetState), (10 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.HAL_UART_GetError), (6 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_RxISR_16BIT), (202 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_RxISR_8BIT), (204 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMAReceiveCplt), (142 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f0xx_hal_uart.o(.text.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32f0xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text), (0 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init), (144 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_RS485Ex_Init), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set), (46 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_MultiProcessorEx_AddressLength_Set), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle), (424 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (102 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (108 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f0xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing system_stm32f0xx.o(.text), (0 bytes).
    Removing system_stm32f0xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f0xx.o(.text.SystemCoreClockUpdate), (96 bytes).
    Removing system_stm32f0xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f0xx.o(.rodata.APBPrescTable), (8 bytes).
    Removing ntc10k.o(.text), (0 bytes).
    Removing ntc10k.o(.ARM.exidx.text.NTC_Read_Resistor), (8 bytes).
    Removing ntc10k.o(.ARM.exidx.text.NTC_Calculate), (8 bytes).

919 unused section(s) (total 54478 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  dscalbn.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    Ntc10k.c                                 0x00000000   Number         0  ntc10k.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    iwdg.c                                   0x00000000   Number         0  iwdg.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f030x8.s                    0x00000000   Number         0  startup_stm32f030x8.o ABSOLUTE
    stm32f0xx_hal.c                          0x00000000   Number         0  stm32f0xx_hal.o ABSOLUTE
    stm32f0xx_hal_adc.c                      0x00000000   Number         0  stm32f0xx_hal_adc.o ABSOLUTE
    stm32f0xx_hal_adc_ex.c                   0x00000000   Number         0  stm32f0xx_hal_adc_ex.o ABSOLUTE
    stm32f0xx_hal_cortex.c                   0x00000000   Number         0  stm32f0xx_hal_cortex.o ABSOLUTE
    stm32f0xx_hal_dma.c                      0x00000000   Number         0  stm32f0xx_hal_dma.o ABSOLUTE
    stm32f0xx_hal_exti.c                     0x00000000   Number         0  stm32f0xx_hal_exti.o ABSOLUTE
    stm32f0xx_hal_flash.c                    0x00000000   Number         0  stm32f0xx_hal_flash.o ABSOLUTE
    stm32f0xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f0xx_hal_flash_ex.o ABSOLUTE
    stm32f0xx_hal_gpio.c                     0x00000000   Number         0  stm32f0xx_hal_gpio.o ABSOLUTE
    stm32f0xx_hal_i2c.c                      0x00000000   Number         0  stm32f0xx_hal_i2c.o ABSOLUTE
    stm32f0xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f0xx_hal_i2c_ex.o ABSOLUTE
    stm32f0xx_hal_iwdg.c                     0x00000000   Number         0  stm32f0xx_hal_iwdg.o ABSOLUTE
    stm32f0xx_hal_msp.c                      0x00000000   Number         0  stm32f0xx_hal_msp.o ABSOLUTE
    stm32f0xx_hal_pwr.c                      0x00000000   Number         0  stm32f0xx_hal_pwr.o ABSOLUTE
    stm32f0xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f0xx_hal_pwr_ex.o ABSOLUTE
    stm32f0xx_hal_rcc.c                      0x00000000   Number         0  stm32f0xx_hal_rcc.o ABSOLUTE
    stm32f0xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f0xx_hal_rcc_ex.o ABSOLUTE
    stm32f0xx_hal_tim.c                      0x00000000   Number         0  stm32f0xx_hal_tim.o ABSOLUTE
    stm32f0xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f0xx_hal_tim_ex.o ABSOLUTE
    stm32f0xx_hal_uart.c                     0x00000000   Number         0  stm32f0xx_hal_uart.o ABSOLUTE
    stm32f0xx_hal_uart_ex.c                  0x00000000   Number         0  stm32f0xx_hal_uart_ex.o ABSOLUTE
    stm32f0xx_it.c                           0x00000000   Number         0  stm32f0xx_it.o ABSOLUTE
    system_stm32f0xx.c                       0x00000000   Number         0  system_stm32f0xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      180  startup_stm32f030x8.o(RESET)
    !!!main                                  0x080000b4   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000bc   Section       60  __scatter.o(!!!scatter)
    !!handler_copy                           0x080000f8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000114   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000130   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x08000132   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x08000132   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x08000134   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000136   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000136   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000138   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000138   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000138   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800013e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800013e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000142   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000142   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800014a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800014c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800014c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000150   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000158   Section       56  startup_stm32f030x8.o(.text)
    .text                                    0x08000190   Section        0  heapauxi.o(.text)
    .text                                    0x08000198   Section        0  d2f.o(.text)
    _dadd1                                   0x08000215   Thumb Code   290  daddsub.o(.text)
    .text                                    0x08000214   Section        0  daddsub.o(.text)
    _dsub1                                   0x08000337   Thumb Code   470  daddsub.o(.text)
    .text                                    0x0800056c   Section        0  ddiv.o(.text)
    .text                                    0x080009b4   Section        0  dflti.o(.text)
    .text                                    0x08000a0c   Section        0  f2d.o(.text)
    .text                                    0x08000a60   Section        0  fdiv.o(.text)
    .text                                    0x08000bc0   Section        0  fflti.o(.text)
    .text                                    0x08000c1e   Section        0  _rserrno.o(.text)
    .text                                    0x08000c34   Section        0  dmul.o(.text)
    .text                                    0x08000e7c   Section        0  dscalbn.o(.text)
    .text                                    0x08000edc   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000ee4   Section        0  dcmpin.o(.text)
    .text                                    0x08000f84   Section        0  fcmpin.o(.text)
    .text                                    0x08000fe8   Section        8  libspace.o(.text)
    .text                                    0x08000ff0   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x0800102e   Section        0  exit.o(.text)
    .text                                    0x0800103e   Section        0  cmpret.o(.text)
    .text                                    0x0800106c   Section        0  dnan2.o(.text)
    .text                                    0x08001080   Section        0  fnan2.o(.text)
    .text                                    0x08001090   Section        0  retnan.o(.text)
    .text                                    0x080010f0   Section        0  sys_exit.o(.text)
    .text                                    0x080010fc   Section        2  use_no_semi.o(.text)
    .text                                    0x080010fe   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x08001100   Section        0  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    __arm_cp.8_0                             0x08001144   Number         4  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    __arm_cp.8_1                             0x08001148   Number         4  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    __arm_cp.8_2                             0x0800114c   Number         4  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    __arm_cp.8_3                             0x08001150   Number         4  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    ADC_Enable                               0x08001155   Thumb Code   172  stm32f0xx_hal_adc.o(.text.ADC_Enable)
    [Anonymous Symbol]                       0x08001154   Section        0  stm32f0xx_hal_adc.o(.text.ADC_Enable)
    __arm_cp.5_0                             0x08001200   Number         4  stm32f0xx_hal_adc.o(.text.ADC_Enable)
    [Anonymous Symbol]                       0x08001204   Section        0  stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler)
    __arm_cp.5_0                             0x08001210   Number         4  stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler)
    [Anonymous Symbol]                       0x08001214   Section        0  stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler)
    __arm_cp.6_0                             0x08001224   Number         4  stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler)
    __arm_cp.6_1                             0x08001228   Number         4  stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler)
    [Anonymous Symbol]                       0x0800122c   Section        0  stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler)
    __arm_cp.7_0                             0x08001238   Number         4  stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler)
    [Anonymous Symbol]                       0x0800123c   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08001244   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_0                            0x08001360   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_1                            0x08001364   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_2                            0x08001368   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_3                            0x0800136c   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_4                            0x08001370   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.22_5                            0x08001374   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    [Anonymous Symbol]                       0x08001378   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback)
    [Anonymous Symbol]                       0x0800137a   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    [Anonymous Symbol]                       0x0800137c   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_GetValue)
    [Anonymous Symbol]                       0x08001382   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler)
    [Anonymous Symbol]                       0x08001498   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_0                             0x080015e8   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_1                             0x080015ec   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_Init)
    [Anonymous Symbol]                       0x080015f0   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback)
    [Anonymous Symbol]                       0x080015f4   Section        0  adc.o(.text.HAL_ADC_MspInit)
    __arm_cp.1_0                             0x08001658   Number         4  adc.o(.text.HAL_ADC_MspInit)
    __arm_cp.1_1                             0x0800165c   Number         4  adc.o(.text.HAL_ADC_MspInit)
    [Anonymous Symbol]                       0x08001660   Section        0  stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT)
    __arm_cp.9_0                             0x080016c8   Number         4  stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT)
    [Anonymous Symbol]                       0x080016cc   Section        0  stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x080017a0   Section        0  stm32f0xx_hal_dma.o(.text.HAL_DMA_Init)
    __arm_cp.0_0                             0x08001810   Number         4  stm32f0xx_hal_dma.o(.text.HAL_DMA_Init)
    __arm_cp.0_1                             0x08001814   Number         4  stm32f0xx_hal_dma.o(.text.HAL_DMA_Init)
    __arm_cp.0_2                             0x08001818   Number         4  stm32f0xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x0800181c   Section        0  stm32f0xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x0800183c   Section        0  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_0                             0x080019d8   Number         4  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_1                             0x080019dc   Number         4  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_3                             0x080019e0   Number         4  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_4                             0x080019e4   Number         4  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_5                             0x080019e8   Number         4  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080019ec   Section        0  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x080019f6   Section        0  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    [Anonymous Symbol]                       0x08001a06   Section        0  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08001a18   Section        0  stm32f0xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001a20   Section        0  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init)
    __arm_cp.0_0                             0x08001a78   Number         4  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init)
    __arm_cp.0_1                             0x08001a7c   Number         4  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init)
    [Anonymous Symbol]                       0x08001a80   Section        0  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Refresh)
    __arm_cp.1_0                             0x08001a8c   Number         4  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Refresh)
    [Anonymous Symbol]                       0x08001a90   Section        0  stm32f0xx_hal.o(.text.HAL_IncTick)
    __arm_cp.5_1                             0x08001aa0   Number         4  stm32f0xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08001aa4   Section        0  stm32f0xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001ac0   Section        0  stm32f0xx_hal.o(.text.HAL_InitTick)
    __arm_cp.1_0                             0x08001afc   Number         4  stm32f0xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001b00   Section        0  stm32f0xx_hal_msp.o(.text.HAL_MspInit)
    __arm_cp.0_0                             0x08001b28   Number         4  stm32f0xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08001b2c   Section        0  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    __arm_cp.1_0                             0x08001b40   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001b44   Section        0  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    __arm_cp.0_0                             0x08001b74   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    __arm_cp.0_1                             0x08001b78   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08001b7c   Section        0  stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_0                             0x08001c70   Number         4  stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_1                             0x08001c74   Number         4  stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_2                             0x08001c78   Number         4  stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    [Anonymous Symbol]                       0x08001c7c   Section        0  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_0                             0x08001db4   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_1                             0x08001db8   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_2                             0x08001dbc   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_3                             0x08001dc0   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_4                             0x08001dc4   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_5                             0x08001dc8   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_6                             0x08001dcc   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_7                             0x08001dd0   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_8                             0x08001dd4   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001dd8   Section        0  stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_0                             0x080021c0   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_1                             0x080021c4   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_2                             0x080021c8   Number         4  stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x080021cc   Section        0  stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_0                             0x080021f4   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_1                             0x080021f8   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_2                             0x080021fc   Number         4  stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08002200   Section        0  stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x0800225c   Section        0  stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x080022a8   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_0                             0x0800237c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_2                             0x08002380   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_3                             0x08002384   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_5                             0x08002388   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_6                             0x0800238c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    __arm_cp.0_7                             0x08002390   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08002394   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_0                             0x08002528   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_1                             0x0800252c   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_2                             0x08002530   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_3                             0x08002534   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_4                             0x08002538   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_5                             0x0800253c   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_6                             0x08002540   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_7                             0x08002544   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.5_8                             0x08002548   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x0800254c   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    __arm_cp.90_0                            0x080026a4   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x080026a8   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    __arm_cp.75_0                            0x0800281c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    [Anonymous Symbol]                       0x08002820   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_0                            0x080028f4   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_1                            0x080028f8   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_2                            0x080028fc   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_3                            0x08002900   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_4                            0x08002904   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_5                            0x08002908   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    __arm_cp.37_6                            0x0800290c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    [Anonymous Symbol]                       0x08002910   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit)
    [Anonymous Symbol]                       0x08002914   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    __arm_cp.77_1                            0x08002b98   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    __arm_cp.77_2                            0x08002b9c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x08002ba0   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    __arm_cp.27_2                            0x08002c74   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    __arm_cp.27_5                            0x08002c78   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    __arm_cp.27_6                            0x08002c7c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    __arm_cp.27_7                            0x08002c80   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x08002c84   Section        0  tim.o(.text.HAL_TIM_PWM_MspInit)
    __arm_cp.4_1                             0x08002cac   Number         4  tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x08002cb0   Section        0  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    __arm_cp.31_0                            0x08002d60   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    __arm_cp.31_1                            0x08002d64   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    __arm_cp.31_2                            0x08002d68   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    __arm_cp.31_3                            0x08002d6c   Number         4  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    [Anonymous Symbol]                       0x08002d70   Section        0  stm32f0xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08002d74   Section        0  adc.o(.text.MX_ADC_Init)
    __arm_cp.0_0                             0x08002dc8   Number         4  adc.o(.text.MX_ADC_Init)
    __arm_cp.0_1                             0x08002dcc   Number         4  adc.o(.text.MX_ADC_Init)
    [Anonymous Symbol]                       0x08002dd0   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08002e24   Section        0  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_1                             0x08002ee8   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_2                             0x08002eec   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_3                             0x08002ef0   Number         4  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08002ef4   Section        0  iwdg.o(.text.MX_IWDG_Init)
    __arm_cp.0_0                             0x08002f14   Number         4  iwdg.o(.text.MX_IWDG_Init)
    __arm_cp.0_1                             0x08002f18   Number         4  iwdg.o(.text.MX_IWDG_Init)
    __arm_cp.0_2                             0x08002f1c   Number         4  iwdg.o(.text.MX_IWDG_Init)
    [Anonymous Symbol]                       0x08002f20   Section        0  tim.o(.text.MX_TIM17_Init)
    __arm_cp.3_0                             0x08002f78   Number         4  tim.o(.text.MX_TIM17_Init)
    __arm_cp.3_2                             0x08002f7c   Number         4  tim.o(.text.MX_TIM17_Init)
    [Anonymous Symbol]                       0x08002f80   Section        0  tim.o(.text.MX_TIM1_Init)
    __arm_cp.0_0                             0x08003090   Number         4  tim.o(.text.MX_TIM1_Init)
    __arm_cp.0_1                             0x08003094   Number         4  tim.o(.text.MX_TIM1_Init)
    __arm_cp.0_2                             0x08003098   Number         4  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x0800309c   Section        0  tim.o(.text.MX_TIM3_Init)
    __arm_cp.2_0                             0x08003150   Number         4  tim.o(.text.MX_TIM3_Init)
    __arm_cp.2_1                             0x08003154   Number         4  tim.o(.text.MX_TIM3_Init)
    __arm_cp.2_2                             0x08003158   Number         4  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x0800315c   Section        0  stm32f0xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08003160   Section        0  ntc10k.o(.text.NTC_Calculate)
    __arm_cp.1_0                             0x080031a8   Number         4  ntc10k.o(.text.NTC_Calculate)
    __arm_cp.1_1                             0x080031ac   Number         4  ntc10k.o(.text.NTC_Calculate)
    __arm_cp.1_3                             0x080031b0   Number         4  ntc10k.o(.text.NTC_Calculate)
    __arm_cp.1_4                             0x080031b4   Number         4  ntc10k.o(.text.NTC_Calculate)
    [Anonymous Symbol]                       0x080031b8   Section        0  ntc10k.o(.text.NTC_Read_Resistor)
    __arm_cp.0_0                             0x08003220   Number         4  ntc10k.o(.text.NTC_Read_Resistor)
    __arm_cp.0_1                             0x08003224   Number         4  ntc10k.o(.text.NTC_Read_Resistor)
    [Anonymous Symbol]                       0x08003228   Section        0  stm32f0xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x0800322a   Section        0  stm32f0xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x0800322c   Section        0  stm32f0xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08003234   Section        0  system_stm32f0xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08003238   Section        0  main.o(.text.main)
    __arm_cp.1_0                             0x08003444   Number         4  main.o(.text.main)
    __arm_cp.1_1                             0x08003448   Number         4  main.o(.text.main)
    __arm_cp.1_2                             0x0800344c   Number         4  main.o(.text.main)
    __arm_cp.1_3                             0x08003450   Number         4  main.o(.text.main)
    __arm_cp.1_4                             0x08003454   Number         4  main.o(.text.main)
    __arm_cp.1_5                             0x08003458   Number         4  main.o(.text.main)
    __arm_cp.1_6                             0x0800345c   Number         4  main.o(.text.main)
    __arm_cp.1_7                             0x08003460   Number         4  main.o(.text.main)
    __arm_cp.1_8                             0x08003464   Number         4  main.o(.text.main)
    __arm_cp.1_9                             0x08003468   Number         4  main.o(.text.main)
    __arm_cp.1_10                            0x0800346c   Number         4  main.o(.text.main)
    __arm_cp.1_11                            0x08003470   Number         4  main.o(.text.main)
    __arm_cp.1_12                            0x08003474   Number         4  main.o(.text.main)
    __arm_cp.1_13                            0x08003478   Number         4  main.o(.text.main)
    __arm_cp.1_14                            0x0800347c   Number         4  main.o(.text.main)
    __arm_cp.1_15                            0x08003480   Number         4  main.o(.text.main)
    .text_divfast                            0x08003484   Section      502  aeabi_sdivfast.o(.text_divfast)
    i.__kernel_poly                          0x0800367a   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08003728   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x0800373c   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x08003746   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08003758   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x0800376c   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._fgeq                                  0x08003780   Section        0  fcmp.o(i._fgeq)
    i._fgr                                   0x08003796   Section        0  fcmp.o(i._fgr)
    i._fleq                                  0x080037ac   Section        0  fcmp.o(i._fleq)
    i._fls                                   0x080037c6   Section        0  fcmp.o(i._fls)
    i.ldexp                                  0x080037dc   Section        0  ldexp.o(i.ldexp)
    i.log                                    0x0800384c   Section        0  log.o(i.log)
    x$fpl$deqf                               0x08003ba0   Section      100  deqf.o(x$fpl$deqf)
    x$fpl$fgeqf                              0x08003c04   Section       84  fgef.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x08003c58   Section       84  flef.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08003cac   Section      176  fmul.o(x$fpl$fmul)
    ddiv_reciptbl                            0x08003d5c   Data         128  ddiv.o(.constdata)
    .constdata                               0x08003d5c   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x08003d5c   Section        0  usenofp.o(x$fpl$usenofp)
    fdiv_tab                                 0x08003ddc   Data          64  fdiv.o(.constdata)
    .constdata                               0x08003ddc   Section       64  fdiv.o(.constdata)
    Lg2                                      0x08003e20   Data          24  log.o(.constdata)
    .constdata                               0x08003e20   Section       56  log.o(.constdata)
    Lg                                       0x08003e38   Data          32  log.o(.constdata)
    .constdata                               0x08003e58   Section        8  qnan.o(.constdata)
    HAL_RCC_GetSysClockFreq.aPLLMULFactorTable 0x08003e70   Data          16  stm32f0xx_hal_rcc.o(.rodata.cst16)
    [Anonymous Symbol]                       0x08003e70   Section        0  stm32f0xx_hal_rcc.o(.rodata.cst16)
    HAL_RCC_GetSysClockFreq.aPredivFactorTable 0x08003e80   Data          16  stm32f0xx_hal_rcc.o(.rodata.cst16)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f0xx_hal.o(.data..L_MergedGlobals)
    .bss                                     0x20000010   Section       96  libspace.o(.bss)
    [Anonymous Symbol]                       0x20000070   Section        0  main.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000007c   Section        0  stm32f0xx_it.o(.bss..L_MergedGlobals)
    Heap_Mem                                 0x200002c0   Data         512  startup_stm32f030x8.o(HEAP)
    HEAP                                     0x200002c0   Section      512  startup_stm32f030x8.o(HEAP)
    Stack_Mem                                0x200004c0   Data        1024  startup_stm32f030x8.o(STACK)
    STACK                                    0x200004c0   Section     1024  startup_stm32f030x8.o(STACK)
    __initial_sp                             0x200008c0   Data           0  startup_stm32f030x8.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000b4   Number         0  startup_stm32f030x8.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f030x8.o(RESET)
    __Vectors_End                            0x080000b4   Data           0  startup_stm32f030x8.o(RESET)
    __main                                   0x080000b5   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000bd   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000bd   Thumb Code    52  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000bd   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080000cd   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080000f9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000115   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000131   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x08000133   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x08000135   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000137   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000139   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000139   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000139   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800013f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800013f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000143   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000143   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800014b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800014d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800014d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000151   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000159   Thumb Code     8  startup_stm32f030x8.o(.text)
    EXTI0_1_IRQHandler                       0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    EXTI2_3_IRQHandler                       0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    EXTI4_15_IRQHandler                      0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    FLASH_IRQHandler                         0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    I2C1_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    I2C2_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    RCC_IRQHandler                           0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    RTC_IRQHandler                           0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    SPI1_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    SPI2_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM14_IRQHandler                         0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM15_IRQHandler                         0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM16_IRQHandler                         0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM17_IRQHandler                         0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM1_BRK_UP_TRG_COM_IRQHandler           0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM1_CC_IRQHandler                       0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM3_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    TIM6_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    USART1_IRQHandler                        0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    USART2_IRQHandler                        0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    WWDG_IRQHandler                          0x0800016b   Thumb Code     0  startup_stm32f030x8.o(.text)
    __user_initial_stackheap                 0x0800016d   Thumb Code     0  startup_stm32f030x8.o(.text)
    __use_two_region_memory                  0x08000191   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000193   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000195   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x08000199   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x08000199   Thumb Code   120  d2f.o(.text)
    __aeabi_dadd                             0x0800050d   Thumb Code     0  daddsub.o(.text)
    _dadd                                    0x0800050d   Thumb Code    26  daddsub.o(.text)
    __aeabi_dsub                             0x08000527   Thumb Code     0  daddsub.o(.text)
    _dsub                                    0x08000527   Thumb Code    22  daddsub.o(.text)
    __aeabi_drsub                            0x0800053d   Thumb Code     0  daddsub.o(.text)
    _drsb                                    0x0800053d   Thumb Code    28  daddsub.o(.text)
    __aeabi_ddiv                             0x0800056d   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x0800056d   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x0800099d   Thumb Code    20  ddiv.o(.text)
    __aeabi_i2d_normalise                    0x080009b5   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x080009f7   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x080009f7   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x08000a07   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x08000a07   Thumb Code     0  dflti.o(.text)
    __aeabi_f2d                              0x08000a0d   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x08000a0d   Thumb Code    80  f2d.o(.text)
    __aeabi_fdiv                             0x08000a61   Thumb Code     0  fdiv.o(.text)
    _fdiv                                    0x08000a61   Thumb Code   334  fdiv.o(.text)
    _frdiv                                   0x08000baf   Thumb Code     8  fdiv.o(.text)
    __aeabi_i2f_normalise                    0x08000bc1   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x08000c09   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x08000c09   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x08000c19   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x08000c19   Thumb Code     0  fflti.o(.text)
    __read_errno                             0x08000c1f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000c29   Thumb Code    12  _rserrno.o(.text)
    __aeabi_dmul                             0x08000c35   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x08000c35   Thumb Code   558  dmul.o(.text)
    __ARM_scalbn                             0x08000e7d   Thumb Code    84  dscalbn.o(.text)
    __aeabi_errno_addr                       0x08000edd   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000edd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000edd   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __fpl_dcmp_InfNaN                        0x08000ee5   Thumb Code   154  dcmpin.o(.text)
    __fpl_fcmp_InfNaN                        0x08000f85   Thumb Code    96  fcmpin.o(.text)
    __user_libspace                          0x08000fe9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000fe9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000fe9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000ff1   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x0800102f   Thumb Code    16  exit.o(.text)
    __fpl_cmpreturn                          0x0800103f   Thumb Code    46  cmpret.o(.text)
    __fpl_dcheck_NaN2                        0x0800106d   Thumb Code    14  dnan2.o(.text)
    __fpl_fcheck_NaN2                        0x08001081   Thumb Code    10  fnan2.o(.text)
    __fpl_return_NaN                         0x08001091   Thumb Code    94  retnan.o(.text)
    _sys_exit                                0x080010f1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080010fd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080010fd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080010ff   Thumb Code     0  indicate_semi.o(.text)
    ADC1_IRQHandler                          0x08001101   Thumb Code    68  stm32f0xx_it.o(.text.ADC1_IRQHandler)
    DMA1_Channel1_IRQHandler                 0x08001205   Thumb Code    12  stm32f0xx_it.o(.text.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_3_IRQHandler               0x08001215   Thumb Code    16  stm32f0xx_it.o(.text.DMA1_Channel2_3_IRQHandler)
    DMA1_Channel4_5_IRQHandler               0x0800122d   Thumb Code    12  stm32f0xx_it.o(.text.DMA1_Channel4_5_IRQHandler)
    Error_Handler                            0x0800123d   Thumb Code     8  main.o(.text.Error_Handler)
    HAL_ADC_ConfigChannel                    0x08001245   Thumb Code   284  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08001379   Thumb Code     2  stm32f0xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ErrorCallback                    0x0800137b   Thumb Code     2  stm32f0xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    HAL_ADC_GetValue                         0x0800137d   Thumb Code     6  stm32f0xx_hal_adc.o(.text.HAL_ADC_GetValue)
    HAL_ADC_IRQHandler                       0x08001383   Thumb Code   278  stm32f0xx_hal_adc.o(.text.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08001499   Thumb Code   336  stm32f0xx_hal_adc.o(.text.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x080015f1   Thumb Code     2  stm32f0xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x080015f5   Thumb Code   100  adc.o(.text.HAL_ADC_MspInit)
    HAL_ADC_Start_IT                         0x08001661   Thumb Code   104  stm32f0xx_hal_adc.o(.text.HAL_ADC_Start_IT)
    HAL_DMA_IRQHandler                       0x080016cd   Thumb Code   212  stm32f0xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080017a1   Thumb Code   112  stm32f0xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_Delay                                0x0800181d   Thumb Code    32  stm32f0xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x0800183d   Thumb Code   412  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080019ed   Thumb Code    10  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x080019f7   Thumb Code    16  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08001a07   Thumb Code    16  stm32f0xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001a19   Thumb Code     8  stm32f0xx_hal.o(.text.HAL_GetTick)
    HAL_IWDG_Init                            0x08001a21   Thumb Code    88  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Init)
    HAL_IWDG_Refresh                         0x08001a81   Thumb Code    12  stm32f0xx_hal_iwdg.o(.text.HAL_IWDG_Refresh)
    HAL_IncTick                              0x08001a91   Thumb Code    16  stm32f0xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08001aa5   Thumb Code    28  stm32f0xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001ac1   Thumb Code    60  stm32f0xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001b01   Thumb Code    40  stm32f0xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001b2d   Thumb Code    20  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001b45   Thumb Code    48  stm32f0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_RCCEx_PeriphCLKConfig                0x08001b7d   Thumb Code   244  stm32f0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08001c7d   Thumb Code   312  stm32f0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_OscConfig                        0x08001dd9   Thumb Code  1000  stm32f0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080021cd   Thumb Code    40  stm32f0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08002201   Thumb Code    90  stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x0800225d   Thumb Code    76  stm32f0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080022a9   Thumb Code   212  stm32f0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002395   Thumb Code   404  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x0800254d   Thumb Code   344  stm32f0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_ConfigChannel                 0x080026a9   Thumb Code   372  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08002821   Thumb Code   212  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08002911   Thumb Code     2  stm32f0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit)
    HAL_TIM_PWM_ConfigChannel                0x08002915   Thumb Code   644  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002ba1   Thumb Code   212  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002c85   Thumb Code    40  tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x08002cb1   Thumb Code   176  stm32f0xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    HardFault_Handler                        0x08002d71   Thumb Code     2  stm32f0xx_it.o(.text.HardFault_Handler)
    MX_ADC_Init                              0x08002d75   Thumb Code    84  adc.o(.text.MX_ADC_Init)
    MX_DMA_Init                              0x08002dd1   Thumb Code    84  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x08002e25   Thumb Code   196  gpio.o(.text.MX_GPIO_Init)
    MX_IWDG_Init                             0x08002ef5   Thumb Code    32  iwdg.o(.text.MX_IWDG_Init)
    MX_TIM17_Init                            0x08002f21   Thumb Code    88  tim.o(.text.MX_TIM17_Init)
    MX_TIM1_Init                             0x08002f81   Thumb Code   272  tim.o(.text.MX_TIM1_Init)
    MX_TIM3_Init                             0x0800309d   Thumb Code   180  tim.o(.text.MX_TIM3_Init)
    NMI_Handler                              0x0800315d   Thumb Code     2  stm32f0xx_it.o(.text.NMI_Handler)
    NTC_Calculate                            0x08003161   Thumb Code    72  ntc10k.o(.text.NTC_Calculate)
    NTC_Read_Resistor                        0x080031b9   Thumb Code   104  ntc10k.o(.text.NTC_Read_Resistor)
    PendSV_Handler                           0x08003229   Thumb Code     2  stm32f0xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x0800322b   Thumb Code     2  stm32f0xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x0800322d   Thumb Code     8  stm32f0xx_it.o(.text.SysTick_Handler)
    SystemInit                               0x08003235   Thumb Code     2  system_stm32f0xx.o(.text.SystemInit)
    main                                     0x08003239   Thumb Code   524  main.o(.text.main)
    __aeabi_uidiv                            0x08003485   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x080034c9   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __kernel_poly                            0x0800367b   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08003729   Thumb Code    16  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x0800373d   Thumb Code    10  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x08003747   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08003759   Thumb Code    16  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x0800376d   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __aeabi_fcmpge                           0x08003781   Thumb Code     0  fcmp.o(i._fgeq)
    _fgeq                                    0x08003781   Thumb Code    22  fcmp.o(i._fgeq)
    __aeabi_fcmpgt                           0x08003797   Thumb Code     0  fcmp.o(i._fgr)
    _fgr                                     0x08003797   Thumb Code    22  fcmp.o(i._fgr)
    __aeabi_fcmple                           0x080037ad   Thumb Code     0  fcmp.o(i._fleq)
    _fleq                                    0x080037ad   Thumb Code    26  fcmp.o(i._fleq)
    __aeabi_fcmplt                           0x080037c7   Thumb Code     0  fcmp.o(i._fls)
    _fls                                     0x080037c7   Thumb Code    22  fcmp.o(i._fls)
    ldexp                                    0x080037dd   Thumb Code   112  ldexp.o(i.ldexp)
    log                                      0x0800384d   Thumb Code   790  log.o(i.log)
    __aeabi_cdcmpeq                          0x08003ba1   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08003ba1   Thumb Code    94  deqf.o(x$fpl$deqf)
    _fcmpge                                  0x08003c05   Thumb Code    78  fgef.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x08003c59   Thumb Code     0  flef.o(x$fpl$fleqf)
    _fcmple                                  0x08003c59   Thumb Code    78  flef.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08003cad   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08003cad   Thumb Code   172  fmul.o(x$fpl$fmul)
    __I$use$fp                               0x08003d5c   Number         0  usenofp.o(x$fpl$usenofp)
    __mathlib_zero                           0x08003e58   Data           8  qnan.o(.constdata)
    AHBPrescTable                            0x08003e60   Data          16  system_stm32f0xx.o(.rodata.AHBPrescTable)
    Region$$Table$$Base                      0x08003e90   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003eb0   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f0xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f0xx_hal.o(.data..L_MergedGlobals)
    PinIn_State                              0x20000008   Data           1  main.o(.data.PinIn_State)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f0xx.o(.data.SystemCoreClock)
    __libspace_start                         0x20000010   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000070   Data           0  libspace.o(.bss)
    state                                    0x20000070   Data           4  main.o(.bss..L_MergedGlobals)
    NTC_value                                0x20000074   Data           4  main.o(.bss..L_MergedGlobals)
    temperature                              0x20000078   Data           4  main.o(.bss..L_MergedGlobals)
    CountLed                                 0x2000007c   Data           4  stm32f0xx_it.o(.bss..L_MergedGlobals)
    ADC_raw                                  0x20000080   Data           4  stm32f0xx_it.o(.bss..L_MergedGlobals)
    hadc                                     0x20000084   Data          64  adc.o(.bss.hadc)
    hdma_tim17_ch1_up                        0x200000c4   Data          68  tim.o(.bss.hdma_tim17_ch1_up)
    hdma_tim3_ch1_trig                       0x20000108   Data          68  tim.o(.bss.hdma_tim3_ch1_trig)
    hdma_tim3_ch3                            0x2000014c   Data          68  tim.o(.bss.hdma_tim3_ch3)
    hdma_tim3_ch4_up                         0x20000190   Data          68  tim.o(.bss.hdma_tim3_ch4_up)
    hiwdg                                    0x200001d4   Data          16  iwdg.o(.bss.hiwdg)
    htim1                                    0x200001e4   Data          72  tim.o(.bss.htim1)
    htim17                                   0x2000022c   Data          72  tim.o(.bss.htim17)
    htim3                                    0x20000274   Data          72  tim.o(.bss.htim3)
    uwTick                                   0x200002bc   Data           4  stm32f0xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000b5

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003ec0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003eb0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000b4   Data   RO            3    RESET               startup_stm32f030x8.o
    0x080000b4   0x080000b4   0x00000008   Code   RO         1225  * !!!main             c_p.l(__main.o)
    0x080000bc   0x080000bc   0x0000003c   Code   RO         1492    !!!scatter          c_p.l(__scatter.o)
    0x080000f8   0x080000f8   0x0000001a   Code   RO         1494    !!handler_copy      c_p.l(__scatter_copy.o)
    0x08000112   0x08000112   0x00000002   PAD
    0x08000114   0x08000114   0x0000001c   Code   RO         1496    !!handler_zi        c_p.l(__scatter_zi.o)
    0x08000130   0x08000130   0x00000002   Code   RO         1348    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1365    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1367    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1369    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1372    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1374    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1376    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1379    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1381    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1383    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1385    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1387    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1389    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1391    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1393    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1395    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1397    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1399    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1403    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1405    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1407    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000000   Code   RO         1409    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x08000132   0x08000132   0x00000002   Code   RO         1410    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x08000134   0x08000134   0x00000002   Code   RO         1447    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1475    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1477    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1480    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1483    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1485    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000000   Code   RO         1488    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x08000136   0x08000136   0x00000002   Code   RO         1489    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1268    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x08000138   0x08000138   0x00000000   Code   RO         1306    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x08000138   0x08000138   0x00000006   Code   RO         1318    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1308    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x0800013e   0x0800013e   0x00000004   Code   RO         1309    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x08000142   0x08000142   0x00000000   Code   RO         1311    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x08000142   0x08000142   0x00000008   Code   RO         1312    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000002   Code   RO         1356    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x0800014c   0x0800014c   0x00000000   Code   RO         1418    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x0800014c   0x0800014c   0x00000004   Code   RO         1419    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x08000150   0x08000150   0x00000006   Code   RO         1420    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x08000156   0x08000156   0x00000002   PAD
    0x08000158   0x08000158   0x00000038   Code   RO            4    .text               startup_stm32f030x8.o
    0x08000190   0x08000190   0x00000006   Code   RO         1223    .text               c_p.l(heapauxi.o)
    0x08000196   0x08000196   0x00000002   PAD
    0x08000198   0x08000198   0x0000007c   Code   RO         1227    .text               fz_ps.l(d2f.o)
    0x08000214   0x08000214   0x00000358   Code   RO         1229    .text               fz_ps.l(daddsub.o)
    0x0800056c   0x0800056c   0x00000448   Code   RO         1231    .text               fz_ps.l(ddiv.o)
    0x080009b4   0x080009b4   0x00000058   Code   RO         1234    .text               fz_ps.l(dflti.o)
    0x08000a0c   0x08000a0c   0x00000054   Code   RO         1236    .text               fz_ps.l(f2d.o)
    0x08000a60   0x08000a60   0x00000160   Code   RO         1250    .text               fz_ps.l(fdiv.o)
    0x08000bc0   0x08000bc0   0x0000005e   Code   RO         1253    .text               fz_ps.l(fflti.o)
    0x08000c1e   0x08000c1e   0x00000016   Code   RO         1273    .text               c_p.l(_rserrno.o)
    0x08000c34   0x08000c34   0x00000248   Code   RO         1277    .text               fz_ps.l(dmul.o)
    0x08000e7c   0x08000e7c   0x00000060   Code   RO         1279    .text               fz_ps.l(dscalbn.o)
    0x08000edc   0x08000edc   0x00000008   Code   RO         1325    .text               c_p.l(rt_errno_addr_intlibspace.o)
    0x08000ee4   0x08000ee4   0x000000a0   Code   RO         1327    .text               fz_ps.l(dcmpin.o)
    0x08000f84   0x08000f84   0x00000064   Code   RO         1329    .text               fz_ps.l(fcmpin.o)
    0x08000fe8   0x08000fe8   0x00000008   Code   RO         1331    .text               c_p.l(libspace.o)
    0x08000ff0   0x08000ff0   0x0000003e   Code   RO         1334    .text               c_p.l(sys_stackheap_outer.o)
    0x0800102e   0x0800102e   0x00000010   Code   RO         1337    .text               c_p.l(exit.o)
    0x0800103e   0x0800103e   0x0000002e   Code   RO         1349    .text               fz_ps.l(cmpret.o)
    0x0800106c   0x0800106c   0x00000014   Code   RO         1351    .text               fz_ps.l(dnan2.o)
    0x08001080   0x08001080   0x00000010   Code   RO         1353    .text               fz_ps.l(fnan2.o)
    0x08001090   0x08001090   0x0000005e   Code   RO         1411    .text               fz_ps.l(retnan.o)
    0x080010ee   0x080010ee   0x00000002   PAD
    0x080010f0   0x080010f0   0x0000000c   Code   RO         1413    .text               c_p.l(sys_exit.o)
    0x080010fc   0x080010fc   0x00000002   Code   RO         1436    .text               c_p.l(use_no_semi.o)
    0x080010fe   0x080010fe   0x00000000   Code   RO         1438    .text               c_p.l(indicate_semi.o)
    0x080010fe   0x080010fe   0x00000002   PAD
    0x08001100   0x08001100   0x00000054   Code   RO          138    .text.ADC1_IRQHandler  stm32f0xx_it.o
    0x08001154   0x08001154   0x000000b0   Code   RO          166    .text.ADC_Enable    stm32f0xx_hal_adc.o
    0x08001204   0x08001204   0x00000010   Code   RO          132    .text.DMA1_Channel1_IRQHandler  stm32f0xx_it.o
    0x08001214   0x08001214   0x00000018   Code   RO          134    .text.DMA1_Channel2_3_IRQHandler  stm32f0xx_it.o
    0x0800122c   0x0800122c   0x00000010   Code   RO          136    .text.DMA1_Channel4_5_IRQHandler  stm32f0xx_it.o
    0x0800123c   0x0800123c   0x00000008   Code   RO           17    .text.Error_Handler  main.o
    0x08001244   0x08001244   0x00000134   Code   RO          200    .text.HAL_ADC_ConfigChannel  stm32f0xx_hal_adc.o
    0x08001378   0x08001378   0x00000002   Code   RO          192    .text.HAL_ADC_ConvCpltCallback  stm32f0xx_hal_adc.o
    0x0800137a   0x0800137a   0x00000002   Code   RO          196    .text.HAL_ADC_ErrorCallback  stm32f0xx_hal_adc.o
    0x0800137c   0x0800137c   0x00000006   Code   RO          188    .text.HAL_ADC_GetValue  stm32f0xx_hal_adc.o
    0x08001382   0x08001382   0x00000116   Code   RO          190    .text.HAL_ADC_IRQHandler  stm32f0xx_hal_adc.o
    0x08001498   0x08001498   0x00000158   Code   RO          156    .text.HAL_ADC_Init  stm32f0xx_hal_adc.o
    0x080015f0   0x080015f0   0x00000002   Code   RO          194    .text.HAL_ADC_LevelOutOfWindowCallback  stm32f0xx_hal_adc.o
    0x080015f2   0x080015f2   0x00000002   PAD
    0x080015f4   0x080015f4   0x0000006c   Code   RO           43    .text.HAL_ADC_MspInit  adc.o
    0x08001660   0x08001660   0x0000006c   Code   RO          174    .text.HAL_ADC_Start_IT  stm32f0xx_hal_adc.o
    0x080016cc   0x080016cc   0x000000d4   Code   RO          538    .text.HAL_DMA_IRQHandler  stm32f0xx_hal_dma.o
    0x080017a0   0x080017a0   0x0000007c   Code   RO          524    .text.HAL_DMA_Init  stm32f0xx_hal_dma.o
    0x0800181c   0x0800181c   0x00000020   Code   RO          294    .text.HAL_Delay     stm32f0xx_hal.o
    0x0800183c   0x0800183c   0x000001b0   Code   RO          500    .text.HAL_GPIO_Init  stm32f0xx_hal_gpio.o
    0x080019ec   0x080019ec   0x0000000a   Code   RO          504    .text.HAL_GPIO_ReadPin  stm32f0xx_hal_gpio.o
    0x080019f6   0x080019f6   0x00000010   Code   RO          508    .text.HAL_GPIO_TogglePin  stm32f0xx_hal_gpio.o
    0x08001a06   0x08001a06   0x00000010   Code   RO          506    .text.HAL_GPIO_WritePin  stm32f0xx_hal_gpio.o
    0x08001a16   0x08001a16   0x00000002   PAD
    0x08001a18   0x08001a18   0x00000008   Code   RO          286    .text.HAL_GetTick   stm32f0xx_hal.o
    0x08001a20   0x08001a20   0x00000060   Code   RO          702    .text.HAL_IWDG_Init  stm32f0xx_hal_iwdg.o
    0x08001a80   0x08001a80   0x00000010   Code   RO          704    .text.HAL_IWDG_Refresh  stm32f0xx_hal_iwdg.o
    0x08001a90   0x08001a90   0x00000014   Code   RO          284    .text.HAL_IncTick   stm32f0xx_hal.o
    0x08001aa4   0x08001aa4   0x0000001c   Code   RO          274    .text.HAL_Init      stm32f0xx_hal.o
    0x08001ac0   0x08001ac0   0x00000040   Code   RO          276    .text.HAL_InitTick  stm32f0xx_hal.o
    0x08001b00   0x08001b00   0x0000002c   Code   RO          148    .text.HAL_MspInit   stm32f0xx_hal_msp.o
    0x08001b2c   0x08001b2c   0x00000018   Code   RO          558    .text.HAL_NVIC_EnableIRQ  stm32f0xx_hal_cortex.o
    0x08001b44   0x08001b44   0x00000038   Code   RO          556    .text.HAL_NVIC_SetPriority  stm32f0xx_hal_cortex.o
    0x08001b7c   0x08001b7c   0x00000100   Code   RO          260    .text.HAL_RCCEx_PeriphCLKConfig  stm32f0xx_hal_rcc_ex.o
    0x08001c7c   0x08001c7c   0x0000015c   Code   RO          229    .text.HAL_RCC_ClockConfig  stm32f0xx_hal_rcc.o
    0x08001dd8   0x08001dd8   0x000003f4   Code   RO          227    .text.HAL_RCC_OscConfig  stm32f0xx_hal_rcc.o
    0x080021cc   0x080021cc   0x00000034   Code   RO          564    .text.HAL_SYSTICK_Config  stm32f0xx_hal_cortex.o
    0x08002200   0x08002200   0x0000005a   Code   RO         1011    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f0xx_hal_tim_ex.o
    0x0800225a   0x0800225a   0x00000002   PAD
    0x0800225c   0x0800225c   0x0000004c   Code   RO         1009    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f0xx_hal_tim_ex.o
    0x080022a8   0x080022a8   0x000000ec   Code   RO          714    .text.HAL_TIM_Base_Init  stm32f0xx_hal_tim.o
    0x08002394   0x08002394   0x000001b8   Code   RO           83    .text.HAL_TIM_Base_MspInit  tim.o
    0x0800254c   0x0800254c   0x0000015c   Code   RO          894    .text.HAL_TIM_ConfigClockSource  stm32f0xx_hal_tim.o
    0x080026a8   0x080026a8   0x00000178   Code   RO          864    .text.HAL_TIM_IC_ConfigChannel  stm32f0xx_hal_tim.o
    0x08002820   0x08002820   0x000000f0   Code   RO          788    .text.HAL_TIM_IC_Init  stm32f0xx_hal_tim.o
    0x08002910   0x08002910   0x00000002   Code   RO          790    .text.HAL_TIM_IC_MspInit  stm32f0xx_hal_tim.o
    0x08002912   0x08002912   0x00000002   PAD
    0x08002914   0x08002914   0x0000028c   Code   RO          868    .text.HAL_TIM_PWM_ConfigChannel  stm32f0xx_hal_tim.o
    0x08002ba0   0x08002ba0   0x000000e4   Code   RO          768    .text.HAL_TIM_PWM_Init  stm32f0xx_hal_tim.o
    0x08002c84   0x08002c84   0x0000002c   Code   RO           81    .text.HAL_TIM_PWM_MspInit  tim.o
    0x08002cb0   0x08002cb0   0x000000c0   Code   RO          776    .text.HAL_TIM_PWM_Start  stm32f0xx_hal_tim.o
    0x08002d70   0x08002d70   0x00000002   Code   RO          124    .text.HardFault_Handler  stm32f0xx_it.o
    0x08002d72   0x08002d72   0x00000002   PAD
    0x08002d74   0x08002d74   0x0000005c   Code   RO           41    .text.MX_ADC_Init   adc.o
    0x08002dd0   0x08002dd0   0x00000054   Code   RO           56    .text.MX_DMA_Init   dma.o
    0x08002e24   0x08002e24   0x000000d0   Code   RO           33    .text.MX_GPIO_Init  gpio.o
    0x08002ef4   0x08002ef4   0x0000002c   Code   RO           64    .text.MX_IWDG_Init  iwdg.o
    0x08002f20   0x08002f20   0x00000060   Code   RO           79    .text.MX_TIM17_Init  tim.o
    0x08002f80   0x08002f80   0x0000011c   Code   RO           73    .text.MX_TIM1_Init  tim.o
    0x0800309c   0x0800309c   0x000000c0   Code   RO           77    .text.MX_TIM3_Init  tim.o
    0x0800315c   0x0800315c   0x00000002   Code   RO          122    .text.NMI_Handler   stm32f0xx_it.o
    0x0800315e   0x0800315e   0x00000002   PAD
    0x08003160   0x08003160   0x00000058   Code   RO         1200    .text.NTC_Calculate  ntc10k.o
    0x080031b8   0x080031b8   0x00000070   Code   RO         1198    .text.NTC_Read_Resistor  ntc10k.o
    0x08003228   0x08003228   0x00000002   Code   RO          128    .text.PendSV_Handler  stm32f0xx_it.o
    0x0800322a   0x0800322a   0x00000002   Code   RO          126    .text.SVC_Handler   stm32f0xx_it.o
    0x0800322c   0x0800322c   0x00000008   Code   RO          130    .text.SysTick_Handler  stm32f0xx_it.o
    0x08003234   0x08003234   0x00000002   Code   RO         1183    .text.SystemInit    system_stm32f0xx.o
    0x08003236   0x08003236   0x00000002   PAD
    0x08003238   0x08003238   0x0000024c   Code   RO           13    .text.main          main.o
    0x08003484   0x08003484   0x000001f6   Code   RO         1216    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x0800367a   0x0800367a   0x000000ac   Code   RO         1302    i.__kernel_poly     m_ps.l(poly.o)
    0x08003726   0x08003726   0x00000002   PAD
    0x08003728   0x08003728   0x00000014   Code   RO         1288    i.__mathlib_dbl_divzero  m_ps.l(dunder.o)
    0x0800373c   0x0800373c   0x0000000a   Code   RO         1289    i.__mathlib_dbl_infnan  m_ps.l(dunder.o)
    0x08003746   0x08003746   0x00000010   Code   RO         1291    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x08003756   0x08003756   0x00000002   PAD
    0x08003758   0x08003758   0x00000014   Code   RO         1292    i.__mathlib_dbl_overflow  m_ps.l(dunder.o)
    0x0800376c   0x0800376c   0x00000014   Code   RO         1294    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x08003780   0x08003780   0x00000016   Code   RO         1239    i._fgeq             fz_ps.l(fcmp.o)
    0x08003796   0x08003796   0x00000016   Code   RO         1240    i._fgr              fz_ps.l(fcmp.o)
    0x080037ac   0x080037ac   0x0000001a   Code   RO         1241    i._fleq             fz_ps.l(fcmp.o)
    0x080037c6   0x080037c6   0x00000016   Code   RO         1242    i._fls              fz_ps.l(fcmp.o)
    0x080037dc   0x080037dc   0x00000070   Code   RO         1259    i.ldexp             m_ps.l(ldexp.o)
    0x0800384c   0x0800384c   0x00000354   Code   RO         1264    i.log               m_ps.l(log.o)
    0x08003ba0   0x08003ba0   0x00000064   Code   RO         1275    x$fpl$deqf          fz_ps.l(deqf.o)
    0x08003c04   0x08003c04   0x00000054   Code   RO         1283    x$fpl$fgeqf         fz_ps.l(fgef.o)
    0x08003c58   0x08003c58   0x00000054   Code   RO         1285    x$fpl$fleqf         fz_ps.l(flef.o)
    0x08003cac   0x08003cac   0x000000b0   Code   RO         1255    x$fpl$fmul          fz_ps.l(fmul.o)
    0x08003d5c   0x08003d5c   0x00000000   Code   RO         1287    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x08003d5c   0x08003d5c   0x00000080   Data   RO         1232    .constdata          fz_ps.l(ddiv.o)
    0x08003ddc   0x08003ddc   0x00000040   Data   RO         1251    .constdata          fz_ps.l(fdiv.o)
    0x08003e1c   0x08003e1c   0x00000004   PAD
    0x08003e20   0x08003e20   0x00000038   Data   RO         1265    .constdata          m_ps.l(log.o)
    0x08003e58   0x08003e58   0x00000008   Data   RO         1304    .constdata          m_ps.l(qnan.o)
    0x08003e60   0x08003e60   0x00000010   Data   RO         1188    .rodata.AHBPrescTable  system_stm32f0xx.o
    0x08003e70   0x08003e70   0x00000020   Data   RO          251    .rodata.cst16       stm32f0xx_hal_rcc.o
    0x08003e90   0x08003e90   0x00000020   Data   RO         1491    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003eb0, Size: 0x000008c0, Max: 0x00002000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003eb0   0x00000008   Data   RW          321    .data..L_MergedGlobals  stm32f0xx_hal.o
    0x20000008   0x08003eb8   0x00000001   Data   RW           19    .data.PinIn_State   main.o
    0x20000009   0x08003eb9   0x00000003   PAD
    0x2000000c   0x08003ebc   0x00000004   Data   RW         1187    .data.SystemCoreClock  system_stm32f0xx.o
    0x20000010        -       0x00000060   Zero   RW         1332    .bss                c_p.l(libspace.o)
    0x20000070        -       0x0000000c   Zero   RW           24    .bss..L_MergedGlobals  main.o
    0x2000007c        -       0x00000008   Zero   RW          140    .bss..L_MergedGlobals  stm32f0xx_it.o
    0x20000084        -       0x00000040   Zero   RW           47    .bss.hadc           adc.o
    0x200000c4        -       0x00000044   Zero   RW           95    .bss.hdma_tim17_ch1_up  tim.o
    0x20000108        -       0x00000044   Zero   RW           92    .bss.hdma_tim3_ch1_trig  tim.o
    0x2000014c        -       0x00000044   Zero   RW           93    .bss.hdma_tim3_ch3  tim.o
    0x20000190        -       0x00000044   Zero   RW           94    .bss.hdma_tim3_ch4_up  tim.o
    0x200001d4        -       0x00000010   Zero   RW           66    .bss.hiwdg          iwdg.o
    0x200001e4        -       0x00000048   Zero   RW           89    .bss.htim1          tim.o
    0x2000022c        -       0x00000048   Zero   RW           91    .bss.htim17         tim.o
    0x20000274        -       0x00000048   Zero   RW           90    .bss.htim3          tim.o
    0x200002bc        -       0x00000004   Zero   RW          320    .bss.uwTick         stm32f0xx_hal.o
    0x200002c0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f030x8.o
    0x200004c0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f030x8.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       200         16          0          0         64       4958   adc.o
        84          0          0          0          0       1618   dma.o
       208         12          0          0          0       1759   gpio.o
        44         12          0          0         16       1181   iwdg.o
       596         70          0          1         12       4333   main.o
       200         24          0          0          0       1228   ntc10k.o
        56         26        180          0       1536        632   startup_stm32f030x8.o
       152          8          0          8          4       5255   stm32f0xx_hal.o
      1226         40          0          0          0      14189   stm32f0xx_hal_adc.o
       132         24          0          0          0       5464   stm32f0xx_hal_cortex.o
       336         12          0          0          0       8046   stm32f0xx_hal_dma.o
       474         20          0          0          0       4972   stm32f0xx_hal_gpio.o
       112         12          0          0          0       1773   stm32f0xx_hal_iwdg.o
        44          4          0          0          0       1022   stm32f0xx_hal_msp.o
      1360         48         32          0          0       7842   stm32f0xx_hal_rcc.o
       256         12          0          0          0       3277   stm32f0xx_hal_rcc_ex.o
      2274        108          0          0          0      62212   stm32f0xx_hal_tim.o
       166          0          0          0          0      22452   stm32f0xx_hal_tim_ex.o
       156         32          0          0          8       2169   stm32f0xx_it.o
         2          0         16          4          0       1823   system_stm32f0xx.o
      1056         72          0          0        488       7926   tim.o

    ----------------------------------------------------------------------
      9148        <USER>        <GROUP>         16       2128     164131   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        60          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0         92   _rserrno.o
       502          0          0          0          0         92   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        46          0          0          0          0         60   cmpret.o
       124          4          0          0          0         72   d2f.o
       856         20          0          0          0        208   daddsub.o
       160          6          0          0          0         76   dcmpin.o
      1096         26        128          0          0        112   ddiv.o
       100          4          0          0          0         92   deqf.o
        88          0          0          0          0         92   dflti.o
       584         26          0          0          0         84   dmul.o
        20          6          0          0          0         68   dnan2.o
        96         12          0          0          0         72   dscalbn.o
        84          4          0          0          0         60   f2d.o
        92          0          0          0          0        272   fcmp.o
       100          4          0          0          0         68   fcmpin.o
       352         10         64          0          0         92   fdiv.o
        94          0          0          0          0         92   fflti.o
        84          4          0          0          0         76   fgef.o
        84          4          0          0          0         76   flef.o
       176          4          0          0          0         80   fmul.o
        16          6          0          0          0         68   fnan2.o
        94          0          0          0          0         68   retnan.o
         0          0          0          0          0          0   usenofp.o
        86         14          0          0          0        340   dunder.o
       112          0          0          0          0         72   ldexp.o
       852         62         56          0          0        136   log.o
       172          0          0          0          0         76   poly.o
         0          0          8          0          0          0   qnan.o

    ----------------------------------------------------------------------
      6380        <USER>        <GROUP>          0         96       3312   Library Totals
        14          0          4          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       798         20          0          0         96        800   c_p.l
      4346        140        192          0          0       1888   fz_ps.l
      1222         76         64          0          0        624   m_ps.l

    ----------------------------------------------------------------------
      6380        <USER>        <GROUP>          0         96       3312   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15528        788        520         16       2224     165427   Grand Totals
     15528        788        520         16       2224     165427   ELF Image Totals
     15528        788        520         16          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                16048 (  15.67kB)
    Total RW  Size (RW Data + ZI Data)              2240 (   2.19kB)
    Total ROM Size (Code + RO Data + RW Data)      16064 (  15.69kB)

==============================================================================

